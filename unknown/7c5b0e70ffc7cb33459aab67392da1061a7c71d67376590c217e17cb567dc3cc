use napi_derive::napi;
use serde::{Deserialize, Serialize};

/// Number of bytes in a signature
pub const SIGNATURE_BYTES: usize = 64;

/// A 64-byte signature.
/// 
/// This is a NAPI-compatible version of solana_signature::Signature
#[napi(object)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct Signature {
    /// The 64-byte signature data
    pub bytes: Vec<u8>,
}

impl Default for Signature {
    fn default() -> Self {
        Self {
            bytes: vec![0u8; SIGNATURE_BYTES],
        }
    }
}

impl Signature {
    /// Create a new Signature from a 64-byte array
    pub fn new_from_array(signature_array: [u8; SIGNATURE_BYTES]) -> Self {
        Self {
            bytes: signature_array.to_vec(),
        }
    }

    /// Create a new Signature from a byte slice
    pub fn new_from_slice(bytes: &[u8]) -> Result<Self, String> {
        if bytes.len() != SIGNATURE_BYTES {
            return Err(format!("Signature must be exactly {} bytes", SIGNATURE_BYTES));
        }
        Ok(Self {
            bytes: bytes.to_vec(),
        })
    }

    /// Convert to bytes array
    pub fn to_bytes(&self) -> [u8; SIGNATURE_BYTES] {
        let mut array = [0u8; SIGNATURE_BYTES];
        array.copy_from_slice(&self.bytes[..SIGNATURE_BYTES]);
        array
    }

    /// Create a unique Signature for testing
    pub fn new_unique() -> Self {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        
        let mut bytes = [0u8; SIGNATURE_BYTES];
        let counter = COUNTER.fetch_add(1, Ordering::SeqCst);
        bytes[0..8].copy_from_slice(&counter.to_le_bytes());
        Self::new_from_array(bytes)
    }
}

impl From<[u8; SIGNATURE_BYTES]> for Signature {
    fn from(bytes: [u8; SIGNATURE_BYTES]) -> Self {
        Self::new_from_array(bytes)
    }
}

impl From<solana_signature::Signature> for Signature {
    fn from(signature: solana_signature::Signature) -> Self {
        Self::new_from_array(signature.into())
    }
}

impl From<Signature> for solana_signature::Signature {
    fn from(signature: Signature) -> Self {
        solana_signature::Signature::from(signature.to_bytes())
    }
}
