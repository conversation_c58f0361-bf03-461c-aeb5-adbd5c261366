use napi_derive::napi;
use serde::{Deserialize, Serialize};

/// Size of a hash in bytes.
pub const HASH_BYTES: usize = 32;

/// A hash; the 32-byte output of a hashing algorithm.
/// 
/// This is a NAPI-compatible version of solana_hash::Hash
#[napi(object)]
#[derive(<PERSON>bug, <PERSON>lone, PartialEq, Eq, Serialize, Deserialize)]
pub struct Hash {
    /// The 32-byte hash data
    pub bytes: Vec<u8>,
}

impl Default for Hash {
    fn default() -> Self {
        Self {
            bytes: vec![0u8; HASH_BYTES],
        }
    }
}

impl Hash {
    /// Create a new Hash from a 32-byte array
    pub fn new_from_array(hash_array: [u8; HASH_BYTES]) -> Self {
        Self {
            bytes: hash_array.to_vec(),
        }
    }

    /// Create a new Hash from a byte slice
    pub fn new_from_slice(bytes: &[u8]) -> Result<Self, String> {
        if bytes.len() != HASH_BYTES {
            return Err(format!("Hash must be exactly {} bytes", HASH_BYTES));
        }
        Ok(Self {
            bytes: bytes.to_vec(),
        })
    }

    /// Convert to bytes array
    pub fn to_bytes(&self) -> [u8; HASH_BYTES] {
        let mut array = [0u8; HASH_BYTES];
        array.copy_from_slice(&self.bytes[..HASH_BYTES]);
        array
    }

    /// Create a unique Hash for testing
    pub fn new_unique() -> Self {
        use std::sync::atomic::{AtomicU64, Ordering};
        static COUNTER: AtomicU64 = AtomicU64::new(1);
        
        let mut bytes = [0u8; HASH_BYTES];
        let counter = COUNTER.fetch_add(1, Ordering::SeqCst);
        bytes[0..8].copy_from_slice(&counter.to_le_bytes());
        Self::new_from_array(bytes)
    }
}

impl From<[u8; HASH_BYTES]> for Hash {
    fn from(bytes: [u8; HASH_BYTES]) -> Self {
        Self::new_from_array(bytes)
    }
}

impl From<solana_hash::Hash> for Hash {
    fn from(hash: solana_hash::Hash) -> Self {
        Self::new_from_array(hash.to_bytes())
    }
}

impl From<Hash> for solana_hash::Hash {
    fn from(hash: Hash) -> Self {
        solana_hash::Hash::new_from_array(hash.to_bytes())
    }
}
