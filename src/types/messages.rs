use napi_derive::napi;
use serde::{Deserialize, Serialize};

/// Simplified version of VersionedMessage for NAPI compatibility
///
/// This is a simplified representation that focuses on the essential data
/// needed for shredstream decoding without the full complexity of Solana's
/// message system.
#[napi(object)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub struct VersionedMessage {
  /// Message version (0 for V0, 255 for Legacy)
  pub version: u8,

  /// Number of required signatures
  pub num_required_signatures: u8,

  /// Number of readonly signed accounts
  pub num_readonly_signed_accounts: u8,

  /// Number of readonly unsigned accounts
  pub num_readonly_unsigned_accounts: u8,

  /// Account keys as base58 strings for easier JS interop
  pub account_keys: Vec<String>,

  /// Recent blockhash as bytes
  pub recent_blockhash: Vec<u8>,

  /// Serialized instructions data
  pub instructions_data: Vec<u8>,
}

impl Default for VersionedMessage {
  fn default() -> Self {
    Self {
      version: 255, // Legacy by default
      num_required_signatures: 0,
      num_readonly_signed_accounts: 0,
      num_readonly_unsigned_accounts: 0,
      account_keys: Vec::new(),
      recent_blockhash: vec![0u8; 32],
      instructions_data: Vec::new(),
    }
  }
}

impl VersionedMessage {
  /// Create a new legacy message
  pub fn new_legacy(
    num_required_signatures: u8,
    num_readonly_signed_accounts: u8,
    num_readonly_unsigned_accounts: u8,
    account_keys: Vec<String>,
    recent_blockhash: Vec<u8>,
    instructions_data: Vec<u8>,
  ) -> Self {
    Self {
      version: 255,
      num_required_signatures,
      num_readonly_signed_accounts,
      num_readonly_unsigned_accounts,
      account_keys,
      recent_blockhash,
      instructions_data,
    }
  }

  /// Create a new V0 message
  pub fn new_v0(
    num_required_signatures: u8,
    num_readonly_signed_accounts: u8,
    num_readonly_unsigned_accounts: u8,
    account_keys: Vec<String>,
    recent_blockhash: Vec<u8>,
    instructions_data: Vec<u8>,
  ) -> Self {
    Self {
      version: 0,
      num_required_signatures,
      num_readonly_signed_accounts,
      num_readonly_unsigned_accounts,
      account_keys,
      recent_blockhash,
      instructions_data,
    }
  }

  /// Check if this is a legacy message
  pub fn is_legacy(&self) -> bool {
    self.version == 255
  }

  /// Check if this is a V0 message
  pub fn is_v0(&self) -> bool {
    self.version == 0
  }

  /// Get the message header
  pub fn header(&self) -> MessageHeader {
    MessageHeader {
      num_required_signatures: self.num_required_signatures,
      num_readonly_signed_accounts: self.num_readonly_signed_accounts,
      num_readonly_unsigned_accounts: self.num_readonly_unsigned_accounts,
    }
  }
}

/// Message header containing signature and account information
#[napi(object)]
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct MessageHeader {
  /// Number of required signatures
  pub num_required_signatures: u8,

  /// Number of readonly signed accounts
  pub num_readonly_signed_accounts: u8,

  /// Number of readonly unsigned accounts
  pub num_readonly_unsigned_accounts: u8,
}
